{% extends "base.html" %}

{% block title %}Compare Algorithms - Movie Recommendation System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item active">Compare Algorithms</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-center py-4">
                <h1 class="display-5 mb-3">
                    <i class="fas fa-chart-bar me-3"></i>Algorithm Comparison
                </h1>
                <p class="lead">
                    Compare different recommendation algorithms side by side to see how they perform for different users.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- User Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Select User for Comparison
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for user in users %}
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm h-100 user-card" data-user-id="{{ user.id }}">
                            <div class="card-body text-center">
                                <div class="avatar-circle bg-primary text-white mb-2 mx-auto">
                                    {{ user.username[0].upper() }}
                                </div>
                                <h6 class="card-title">{{ user.username }}</h6>
                                <p class="card-text small text-muted">
                                    {{ user.age }} years • {{ user.occupation }}
                                </p>
                                <button class="btn btn-outline-primary btn-sm" 
                                        onclick="compareAlgorithms({{ user.id }}, '{{ user.username }}')">
                                    <i class="fas fa-chart-bar me-1"></i>Compare
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div id="results-section" style="display: none;">
    <div class="row mb-3">
        <div class="col-12">
            <h3 id="results-title" class="text-center"></h3>
        </div>
    </div>
    
    <div class="row">
        <!-- User-Based Collaborative Filtering -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>User-Based Collaborative Filtering
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Finds users with similar preferences and recommends movies they liked.
                    </p>
                    <div id="user-based-results">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Item-Based Collaborative Filtering -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-film me-2"></i>Item-Based Collaborative Filtering
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Recommends movies similar to ones you've already rated highly.
                    </p>
                    <div id="item-based-results">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content-Based Filtering -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Content-Based Filtering
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Uses movie features like genre, director, and description for recommendations.
                    </p>
                    <div id="content-based-results">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hybrid Approach -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-purple text-white" style="background-color: #6f42c1;">
                    <h5 class="mb-0">
                        <i class="fas fa-magic me-2"></i>Hybrid Approach
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Combines collaborative and content-based approaches for better results.
                    </p>
                    <div id="hybrid-results">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Popular Movies (Baseline) -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2"></i>Popular Movies (Baseline)
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Most popular movies based on average ratings - used as a baseline comparison.
                    </p>
                    <div id="popular-results">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Algorithm Explanation -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Algorithm Explanations
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-users text-info me-2"></i>User-Based Collaborative Filtering</h6>
                        <p class="small text-muted mb-3">
                            This algorithm finds users who have similar rating patterns to the target user. 
                            It then recommends movies that these similar users have rated highly but the target user hasn't seen yet.
                        </p>
                        
                        <h6><i class="fas fa-film text-success me-2"></i>Item-Based Collaborative Filtering</h6>
                        <p class="small text-muted mb-3">
                            This approach looks at the movies the user has rated highly and finds other movies 
                            that are similar based on how other users have rated them. It's often more stable than user-based filtering.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-tags text-warning me-2"></i>Content-Based Filtering</h6>
                        <p class="small text-muted mb-3">
                            This method analyzes the content features of movies (genres, directors, descriptions) 
                            and recommends movies with similar characteristics to those the user has liked before.
                        </p>
                        
                        <h6><i class="fas fa-magic text-purple me-2" style="color: #6f42c1;"></i>Hybrid Approach</h6>
                        <p class="small text-muted mb-3">
                            Combines collaborative filtering and content-based filtering to leverage the strengths 
                            of both approaches, often providing more robust and diverse recommendations.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function compareAlgorithms(userId, username) {
    // Show results section
    document.getElementById('results-section').style.display = 'block';
    document.getElementById('results-title').textContent = `Recommendations for ${username}`;
    
    // Reset all result containers
    const containers = ['user-based-results', 'item-based-results', 'content-based-results', 'hybrid-results', 'popular-results'];
    containers.forEach(containerId => {
        document.getElementById(containerId).innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
    });
    
    // Scroll to results
    document.getElementById('results-section').scrollIntoView({ behavior: 'smooth' });
    
    // Fetch comparison data
    fetch(`/api/compare/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data.results);
            } else {
                console.error('Error fetching comparison data');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function displayResults(results) {
    const algorithms = ['user_based', 'item_based', 'content_based', 'hybrid', 'popular'];
    const containerMap = {
        'user_based': 'user-based-results',
        'item_based': 'item-based-results',
        'content_based': 'content-based-results',
        'hybrid': 'hybrid-results',
        'popular': 'popular-results'
    };
    
    algorithms.forEach(algorithm => {
        const containerId = containerMap[algorithm];
        const container = document.getElementById(containerId);
        
        if (results[algorithm] && results[algorithm].error) {
            container.innerHTML = `<div class="alert alert-warning small">Error: ${results[algorithm].error}</div>`;
        } else if (results[algorithm] && results[algorithm].length > 0) {
            let html = '';
            results[algorithm].forEach((movie, index) => {
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${index + 1}. ${movie.title}</strong>
                            <small class="text-muted d-block">${movie.year}</small>
                        </div>
                        <span class="badge bg-primary">${movie.predicted_rating}</span>
                    </div>
                `;
            });
            container.innerHTML = html;
        } else {
            container.innerHTML = '<div class="text-muted text-center">No recommendations available</div>';
        }
    });
}
</script>
{% endblock %}
