/* Custom styles for Movie Recommendation System */

/* Avatar circles */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.avatar-circle-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 32px;
}

/* Card hover effects */
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.user-card {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.user-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15) !important;
}

/* Rating stars */
.rating-stars {
    font-size: 24px;
    cursor: pointer;
}

.rating-star {
    color: #ddd;
    transition: color 0.2s ease-in-out;
    margin: 0 2px;
}

.rating-star:hover {
    color: #ffc107;
}

.rating-star.text-warning {
    color: #ffc107 !important;
}

.rating-star.text-muted {
    color: #6c757d !important;
}

/* Gradient backgrounds */
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-purple {
    background-color: #6f42c1 !important;
}

/* Movie grid styling */
.movie-grid {
    max-height: 400px;
    overflow-y: auto;
}

.movie-grid::-webkit-scrollbar {
    width: 6px;
}

.movie-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.movie-grid::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.movie-grid::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Recommendation cards */
.recommendation-card {
    transition: all 0.2s ease-in-out;
    border-left: 4px solid transparent;
}

.recommendation-card:hover {
    border-left-color: #007bff;
    background-color: #f8f9fa;
}

/* Algorithm comparison cards */
.algorithm-card {
    min-height: 300px;
}

.algorithm-card .card-body {
    display: flex;
    flex-direction: column;
}

.algorithm-card .card-text {
    flex-grow: 1;
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Badge styling */
.badge {
    font-size: 0.75em;
}

.badge-rating {
    background-color: #ffc107;
    color: #212529;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .avatar-circle-large {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .rating-stars {
        font-size: 20px;
    }
}

/* Custom button styles */
.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.btn-outline-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(40,167,69,0.3);
}

/* Chart container */
.chart-container {
    position: relative;
    height: 200px;
    width: 100%;
}

/* Jumbotron alternative for Bootstrap 5 */
.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    border-radius: 0.375rem;
}

/* Custom scrollbar for all containers */
.custom-scroll {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

.custom-scroll::-webkit-scrollbar {
    width: 6px;
}

.custom-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Movie poster placeholder */
.movie-poster {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Animation for cards appearing */
.card-appear {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success/Error message styling */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* Footer styling */
footer {
    margin-top: auto;
}

/* Ensure body takes full height */
html, body {
    height: 100%;
}

#root, .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}
