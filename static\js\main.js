// Main JavaScript file for Movie Recommendation System

// Global variables
let currentUser = null;
let currentMovie = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Add loading states to buttons
    addLoadingStates();
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Add smooth scrolling to anchor links
    addSmoothScrolling();
    
    // Initialize card animations
    animateCards();
}

function addLoadingStates() {
    // Add loading state to buttons when clicked
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('no-loading')) {
                showButtonLoading(this);
            }
        });
    });
}

function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;
    
    // Reset after 3 seconds (fallback)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 3000);
}

function hideButtonLoading(button, originalText) {
    button.innerHTML = originalText;
    button.disabled = false;
}

function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function animateCards() {
    // Add animation class to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('card-appear');
        }, index * 100);
    });
}

// API helper functions
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        showNotification('Error connecting to server', 'error');
        throw error;
    }
}

// Notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// Rating system functions
function initializeRatingSystem() {
    const ratingStars = document.querySelectorAll('.rating-star');
    
    ratingStars.forEach(star => {
        star.addEventListener('mouseenter', function() {
            highlightStars(parseInt(this.dataset.rating));
        });
        
        star.addEventListener('click', function() {
            selectRating(parseInt(this.dataset.rating));
        });
    });
    
    // Reset on mouse leave
    const ratingContainer = document.querySelector('.rating-stars');
    if (ratingContainer) {
        ratingContainer.addEventListener('mouseleave', function() {
            resetStars();
        });
    }
}

function highlightStars(rating) {
    const stars = document.querySelectorAll('.rating-star');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.add('text-warning');
            star.classList.remove('text-muted');
        } else {
            star.classList.add('text-muted');
            star.classList.remove('text-warning');
        }
    });
}

function selectRating(rating) {
    window.selectedRating = rating;
    document.getElementById('selected-rating').textContent = rating;
    highlightStars(rating);
}

function resetStars() {
    if (window.selectedRating) {
        highlightStars(window.selectedRating);
    } else {
        const stars = document.querySelectorAll('.rating-star');
        stars.forEach(star => {
            star.classList.add('text-muted');
            star.classList.remove('text-warning');
        });
    }
}

// Recommendation functions
async function loadRecommendations(userId, algorithm = 'hybrid', containerId = 'recommendations-container') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // Show loading
    container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
    
    try {
        const data = await apiRequest(`/api/recommendations/${userId}?algorithm=${algorithm}&count=8`);
        
        if (data.success) {
            displayRecommendations(data.recommendations, containerId);
        } else {
            container.innerHTML = '<div class="alert alert-warning">No recommendations available</div>';
        }
    } catch (error) {
        container.innerHTML = '<div class="alert alert-danger">Error loading recommendations</div>';
    }
}

function displayRecommendations(recommendations, containerId) {
    const container = document.getElementById(containerId);
    
    if (recommendations.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-magic fa-3x mb-3"></i><p>No recommendations available.</p></div>';
        return;
    }
    
    let html = '<div class="custom-scroll" style="max-height: 400px; overflow-y: auto;">';
    recommendations.forEach(rec => {
        html += `
            <div class="card border-0 shadow-sm mb-2 recommendation-card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h6 class="mb-1">${rec.title}</h6>
                            <small class="text-muted">${rec.year} • ${rec.director}</small>
                            <div class="mt-1">
                                ${rec.genres.slice(0, 2).map(genre => `<span class="badge bg-light text-dark me-1">${genre}</span>`).join('')}
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <span class="badge bg-primary">
                                <i class="fas fa-thumbs-up me-1"></i>${rec.predicted_rating}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    container.innerHTML = html;
}

// Movie rating functions
async function rateMovie(movieId, userId) {
    currentMovie = movieId;
    currentUser = userId;
    
    try {
        // Get movie info
        const moviesData = await apiRequest('/api/movies');
        const movie = moviesData.movies.find(m => m.id === movieId);
        
        if (movie) {
            document.getElementById('movie-info').innerHTML = `
                <h6>${movie.title} (${movie.year})</h6>
                <p class="text-muted">${movie.director}</p>
            `;
        }
        
        // Reset rating
        window.selectedRating = 0;
        document.getElementById('selected-rating').textContent = '0';
        resetStars();
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('ratingModal'));
        modal.show();
        
    } catch (error) {
        showNotification('Error loading movie information', 'error');
    }
}

async function submitRating() {
    if (!window.selectedRating || window.selectedRating === 0) {
        showNotification('Please select a rating', 'warning');
        return;
    }
    
    try {
        const data = await apiRequest('/api/rate', {
            method: 'POST',
            body: JSON.stringify({
                user_id: currentUser,
                movie_id: currentMovie,
                rating: window.selectedRating
            })
        });
        
        if (data.success) {
            showNotification('Rating submitted successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('ratingModal')).hide();
            
            // Refresh page after a short delay
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Error submitting rating: ' + data.error, 'error');
        }
    } catch (error) {
        showNotification('Error submitting rating', 'error');
    }
}

// Utility functions
function formatRating(rating) {
    return Math.round(rating * 10) / 10;
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Search functionality (if needed)
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        const debouncedSearch = debounce(performSearch, 300);
        searchInput.addEventListener('input', debouncedSearch);
    }
}

function performSearch(event) {
    const query = event.target.value.toLowerCase();
    const items = document.querySelectorAll('.searchable-item');
    
    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

// Export functions for global use
window.loadRecommendations = loadRecommendations;
window.rateMovie = rateMovie;
window.submitRating = submitRating;
window.showNotification = showNotification;
