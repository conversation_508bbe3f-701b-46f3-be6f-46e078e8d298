from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class User(db.Model):
    """User model for storing user information."""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    age = db.Column(db.Integer)
    gender = db.Column(db.String(10))
    occupation = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    ratings = db.relationship('Rating', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'age': self.age,
            'gender': self.gender,
            'occupation': self.occupation
        }

class Movie(db.Model):
    """Movie model for storing movie information."""
    __tablename__ = 'movies'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    year = db.Column(db.Integer)
    genres = db.Column(db.Text)  # JSON string of genres list
    director = db.Column(db.String(100))
    description = db.Column(db.Text)
    imdb_rating = db.Column(db.Float)
    duration = db.Column(db.Integer)  # in minutes
    
    # Relationships
    ratings = db.relationship('Rating', backref='movie', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Movie {self.title} ({self.year})>'
    
    @property
    def genres_list(self):
        """Return genres as a list."""
        if self.genres:
            return json.loads(self.genres)
        return []
    
    @genres_list.setter
    def genres_list(self, value):
        """Set genres from a list."""
        self.genres = json.dumps(value)
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'year': self.year,
            'genres': self.genres_list,
            'director': self.director,
            'description': self.description,
            'imdb_rating': self.imdb_rating,
            'duration': self.duration
        }

class Rating(db.Model):
    """Rating model for storing user ratings of movies."""
    __tablename__ = 'ratings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    movie_id = db.Column(db.Integer, db.ForeignKey('movies.id'), nullable=False)
    rating = db.Column(db.Float, nullable=False)  # 1-5 scale
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Unique constraint to prevent duplicate ratings
    __table_args__ = (db.UniqueConstraint('user_id', 'movie_id', name='unique_user_movie_rating'),)
    
    def __repr__(self):
        return f'<Rating User:{self.user_id} Movie:{self.movie_id} Rating:{self.rating}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'movie_id': self.movie_id,
            'rating': self.rating,
            'timestamp': self.timestamp.isoformat()
        }
