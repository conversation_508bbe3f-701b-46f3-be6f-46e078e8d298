{% extends "base.html" %}

{% block title %}Home - Movie Recommendation System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-gradient text-white p-5 rounded mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <h1 class="display-4">
                <i class="fas fa-magic me-3"></i>Movie Recommendation System
            </h1>
            <p class="lead">
                Discover your next favorite movie using advanced machine learning algorithms including 
                collaborative filtering, content-based filtering, and hybrid approaches.
            </p>
            <hr class="my-4">
            <p>
                Select a user below to see personalized recommendations or explore our algorithm comparison tool.
            </p>
        </div>
    </div>
</div>

<div class="row">
    <!-- Users Section -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Users ({{ users|length }})
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Click on a user to see their profile and personalized recommendations.</p>
                <div class="row">
                    {% for user in users %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="avatar-circle bg-primary text-white mb-2 mx-auto">
                                    {{ user.username[0].upper() }}
                                </div>
                                <h6 class="card-title">{{ user.username }}</h6>
                                <p class="card-text small text-muted">
                                    {{ user.age }} years • {{ user.occupation }}
                                </p>
                                <a href="{{ url_for('user_profile', user_id=user.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-user me-1"></i>View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Movies Section -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-film me-2"></i>Movies ({{ movies|length }})
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Browse our movie collection and see detailed information.</p>
                <div class="movie-grid" style="max-height: 400px; overflow-y: auto;">
                    {% for movie in movies %}
                    <div class="card border-0 shadow-sm mb-2">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-8">
                                    <h6 class="card-title mb-1">
                                        <a href="{{ url_for('movie_details', movie_id=movie.id) }}" 
                                           class="text-decoration-none">
                                            {{ movie.title }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        {{ movie.year }} • {{ movie.director }}
                                    </small>
                                </div>
                                <div class="col-4 text-end">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>{{ movie.imdb_rating }}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-1">
                                {% for genre in movie.genres_list[:3] %}
                                <span class="badge bg-light text-dark me-1">{{ genre }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mt-4">
    <div class="col-12">
        <h3 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>Recommendation Algorithms
        </h3>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h5>User-Based</h5>
                <p class="text-muted small">
                    Finds users with similar preferences and recommends movies they liked.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-film fa-2x text-success mb-3"></i>
                <h5>Item-Based</h5>
                <p class="text-muted small">
                    Recommends movies similar to ones you've already rated highly.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-tags fa-2x text-info mb-3"></i>
                <h5>Content-Based</h5>
                <p class="text-muted small">
                    Uses movie features like genre, director, and description for recommendations.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-magic fa-2x text-warning mb-3"></i>
                <h5>Hybrid</h5>
                <p class="text-muted small">
                    Combines collaborative and content-based approaches for better results.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{{ url_for('compare_algorithms') }}" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-chart-bar me-2"></i>Compare Algorithms
        </a>
        <a href="/api/movies" target="_blank" class="btn btn-outline-secondary btn-lg">
            <i class="fas fa-code me-2"></i>Explore API
        </a>
    </div>
</div>
{% endblock %}
