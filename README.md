# Movie Recommendation System

A comprehensive, out-of-the-box movie recommendation system that implements multiple machine learning algorithms including collaborative filtering, content-based filtering, and hybrid approaches.

## Features

### 🎯 Multiple Recommendation Algorithms
- **User-Based Collaborative Filtering**: Finds users with similar preferences
- **Item-Based Collaborative Filtering**: Recommends similar movies
- **Content-Based Filtering**: Uses movie features (genre, director, description)
- **Hybrid Approach**: Combines collaborative and content-based methods
- **Popular Movies**: Baseline recommendations based on average ratings

### 🎬 Interactive Web Interface
- User profiles with personalized recommendations
- Movie details with ratings and reviews
- Real-time rating system
- Algorithm comparison tool
- Responsive design with Bootstrap

### 📊 Data Management
- SQLite database with sample data
- RESTful API endpoints
- Automatic data loading and preprocessing
- Rating management system

### 🔧 Technical Features
- Flask web framework
- scikit-learn for machine learning
- Pandas for data manipulation
- Chart.js for visualizations
- Bootstrap for responsive UI

## Quick Start

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Installation

1. **Clone or download the project**
   ```bash
   cd recommendation-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser**
   Navigate to `http://localhost:5000`

That's it! The system will automatically:
- Create the database
- Load sample data (movies, users, ratings)
- Initialize the recommendation engine
- Start the web server

## Usage

### Web Interface

1. **Home Page**: Browse users and movies
2. **User Profiles**: View ratings and get personalized recommendations
3. **Movie Details**: See movie information and user ratings
4. **Algorithm Comparison**: Compare different recommendation approaches
5. **Rate Movies**: Add your own ratings to improve recommendations

### API Endpoints

- `GET /api/movies` - Get all movies
- `GET /api/users` - Get all users
- `GET /api/recommendations/<user_id>` - Get recommendations for a user
- `POST /api/rate` - Submit a movie rating
- `GET /api/compare/<user_id>` - Compare algorithms for a user

### Example API Usage

```python
import requests

# Get recommendations for user 1 using hybrid algorithm
response = requests.get('http://localhost:5000/api/recommendations/1?algorithm=hybrid&count=5')
recommendations = response.json()

# Rate a movie
rating_data = {
    'user_id': 1,
    'movie_id': 5,
    'rating': 4.5
}
response = requests.post('http://localhost:5000/api/rate', json=rating_data)
```

## Sample Data

The system comes with pre-loaded sample data:

- **12 Movies**: Popular films across different genres
- **8 Users**: Diverse user profiles with different preferences
- **60+ Ratings**: Realistic rating patterns based on user preferences

### Sample Movies Include:
- The Shawshank Redemption (1994)
- The Godfather (1972)
- The Dark Knight (2008)
- Pulp Fiction (1994)
- And more...

## Algorithm Details

### User-Based Collaborative Filtering
- Calculates user similarity using cosine similarity
- Finds users with similar rating patterns
- Recommends movies liked by similar users
- Works well when users have similar preferences

### Item-Based Collaborative Filtering
- Calculates movie similarity based on user ratings
- More stable than user-based approach
- Recommends movies similar to user's highly-rated films
- Better for systems with more movies than users

### Content-Based Filtering
- Uses TF-IDF vectorization on movie features
- Analyzes genres, directors, and descriptions
- Creates user profiles based on content preferences
- Good for new users (cold start problem)

### Hybrid Approach
- Combines collaborative (60%) and content-based (40%) methods
- Leverages strengths of both approaches
- More robust and diverse recommendations
- Configurable weights in `config.py`

## Configuration

Edit `config.py` to customize:

```python
# Algorithm weights for hybrid recommendations
COLLABORATIVE_WEIGHT = 0.6
CONTENT_BASED_WEIGHT = 0.4

# Minimum ratings required for recommendations
MIN_RATINGS_FOR_RECOMMENDATION = 5

# Default number of recommendations
DEFAULT_RECOMMENDATIONS_COUNT = 10

# Similarity threshold for collaborative filtering
SIMILARITY_THRESHOLD = 0.1
```

## Project Structure

```
recommendation-system/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── models.py             # Database models
├── recommendation_engine.py  # ML algorithms
├── data_loader.py        # Data management
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── user_profile.html
│   ├── movie_details.html
│   └── compare.html
├── static/              # CSS and JavaScript
│   ├── css/style.css
│   └── js/main.js
└── data/               # Database and CSV files (auto-created)
    ├── recommendations.db
    ├── movies.csv
    ├── users.csv
    └── ratings.csv
```

## Extending the System

### Adding New Movies
```python
from models import Movie, db

movie = Movie(
    title="New Movie",
    year=2023,
    genres_list=["Action", "Sci-Fi"],
    director="Director Name",
    description="Movie description",
    imdb_rating=8.5,
    duration=120
)
db.session.add(movie)
db.session.commit()
```

### Adding New Users
```python
from models import User, db

user = User(
    username="new_user",
    email="<EMAIL>",
    age=25,
    gender="F",
    occupation="Engineer"
)
db.session.add(user)
db.session.commit()
```

### Custom Algorithms
Extend the `RecommendationEngine` class in `recommendation_engine.py` to add new algorithms.

## Performance Considerations

- **Scalability**: Current implementation works well for small to medium datasets
- **Real-time Updates**: Matrices are recalculated when new ratings are added
- **Memory Usage**: User-item matrices are stored in memory
- **Optimization**: For large datasets, consider using sparse matrices and incremental updates

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Database Issues**: Delete the `data/` folder and restart to recreate
   ```bash
   rm -rf data/
   python app.py
   ```

3. **Port Already in Use**: Change the port in `app.py`
   ```python
   app.run(debug=True, host='0.0.0.0', port=5001)
   ```

## License

This project is open source and available under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For questions or issues, please check the troubleshooting section or create an issue in the repository.
