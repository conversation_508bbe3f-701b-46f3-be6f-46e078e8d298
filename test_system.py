#!/usr/bin/env python3
"""
Test script for the Movie Recommendation System
Demonstrates the functionality of different recommendation algorithms
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_api_endpoints():
    """Test all API endpoints to ensure they're working."""
    print("🎬 Testing Movie Recommendation System API")
    print("=" * 50)
    
    # Test movies endpoint
    print("\n1. Testing Movies API...")
    response = requests.get(f"{BASE_URL}/api/movies")
    if response.status_code == 200:
        movies = response.json()
        print(f"✅ Found {len(movies['movies'])} movies")
        print(f"   Sample: {movies['movies'][0]['title']} ({movies['movies'][0]['year']})")
    else:
        print("❌ Movies API failed")
        return False
    
    # Test users endpoint
    print("\n2. Testing Users API...")
    response = requests.get(f"{BASE_URL}/api/users")
    if response.status_code == 200:
        users = response.json()
        print(f"✅ Found {len(users['users'])} users")
        print(f"   Sample: {users['users'][0]['username']} - {users['users'][0]['occupation']}")
    else:
        print("❌ Users API failed")
        return False
    
    # Test recommendations for each algorithm
    print("\n3. Testing Recommendation Algorithms...")
    user_id = 1
    algorithms = ['user_based', 'item_based', 'content_based', 'hybrid', 'popular']
    
    for algorithm in algorithms:
        print(f"\n   Testing {algorithm.replace('_', '-').title()} Algorithm...")
        response = requests.get(f"{BASE_URL}/api/recommendations/{user_id}?algorithm={algorithm}&count=3")
        if response.status_code == 200:
            recs = response.json()
            if recs['success'] and recs['recommendations']:
                print(f"   ✅ {algorithm}: {len(recs['recommendations'])} recommendations")
                for i, rec in enumerate(recs['recommendations'][:2], 1):
                    print(f"      {i}. {rec['title']} (Score: {rec['predicted_rating']})")
            else:
                print(f"   ⚠️  {algorithm}: No recommendations available")
        else:
            print(f"   ❌ {algorithm}: API failed")
    
    # Test algorithm comparison
    print("\n4. Testing Algorithm Comparison...")
    response = requests.get(f"{BASE_URL}/api/compare/{user_id}")
    if response.status_code == 200:
        comparison = response.json()
        if comparison['success']:
            print("✅ Algorithm comparison working")
            for algo, results in comparison['results'].items():
                if isinstance(results, list) and results:
                    print(f"   {algo}: {results[0]['title']} (Score: {results[0]['predicted_rating']})")
        else:
            print("❌ Algorithm comparison failed")
    else:
        print("❌ Algorithm comparison API failed")
    
    return True

def demonstrate_rating_system():
    """Demonstrate the rating system."""
    print("\n\n🌟 Testing Rating System")
    print("=" * 50)
    
    # Add a new rating
    rating_data = {
        'user_id': 1,
        'movie_id': 12,  # Interstellar
        'rating': 5.0
    }
    
    print(f"Adding rating: User 1 rates Movie 12 with 5.0 stars...")
    response = requests.post(f"{BASE_URL}/api/rate", json=rating_data)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ Rating added successfully!")
            
            # Get updated recommendations
            print("\nGetting updated recommendations...")
            time.sleep(1)  # Give system time to update
            response = requests.get(f"{BASE_URL}/api/recommendations/1?algorithm=hybrid&count=3")
            if response.status_code == 200:
                recs = response.json()
                print("✅ Updated recommendations:")
                for i, rec in enumerate(recs['recommendations'], 1):
                    print(f"   {i}. {rec['title']} (Score: {rec['predicted_rating']})")
        else:
            print(f"❌ Rating failed: {result.get('error', 'Unknown error')}")
    else:
        print("❌ Rating API request failed")

def show_system_overview():
    """Show an overview of the system capabilities."""
    print("\n\n📊 System Overview")
    print("=" * 50)
    
    # Get system statistics
    movies_response = requests.get(f"{BASE_URL}/api/movies")
    users_response = requests.get(f"{BASE_URL}/api/users")
    
    if movies_response.status_code == 200 and users_response.status_code == 200:
        movies = movies_response.json()['movies']
        users = users_response.json()['users']
        
        print(f"📽️  Movies in database: {len(movies)}")
        print(f"👥 Users in database: {len(users)}")
        
        # Show genre distribution
        genres = {}
        for movie in movies:
            for genre in movie['genres']:
                genres[genre] = genres.get(genre, 0) + 1
        
        print(f"\n🎭 Genre Distribution:")
        for genre, count in sorted(genres.items(), key=lambda x: x[1], reverse=True):
            print(f"   {genre}: {count} movies")
        
        # Show year distribution
        years = {}
        for movie in movies:
            decade = (movie['year'] // 10) * 10
            years[decade] = years.get(decade, 0) + 1
        
        print(f"\n📅 Movies by Decade:")
        for decade, count in sorted(years.items()):
            print(f"   {decade}s: {count} movies")
        
        print(f"\n🏆 Top Rated Movies (IMDb):")
        sorted_movies = sorted(movies, key=lambda x: x['imdb_rating'], reverse=True)
        for i, movie in enumerate(sorted_movies[:5], 1):
            print(f"   {i}. {movie['title']} ({movie['year']}) - {movie['imdb_rating']}")

def main():
    """Main test function."""
    print("🚀 Movie Recommendation System Test Suite")
    print("=" * 60)
    
    try:
        # Test if server is running
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding. Make sure to run 'python app.py' first.")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server. Make sure to run 'python app.py' first.")
        return
    
    print("✅ Server is running!")
    
    # Run tests
    if test_api_endpoints():
        demonstrate_rating_system()
        show_system_overview()
        
        print("\n\n🎉 All tests completed successfully!")
        print("\n📱 Web Interface:")
        print(f"   Home Page: {BASE_URL}")
        print(f"   User Profile: {BASE_URL}/user/1")
        print(f"   Movie Details: {BASE_URL}/movie/1")
        print(f"   Algorithm Comparison: {BASE_URL}/compare")
        
        print("\n🔗 API Endpoints:")
        print(f"   Movies: {BASE_URL}/api/movies")
        print(f"   Users: {BASE_URL}/api/users")
        print(f"   Recommendations: {BASE_URL}/api/recommendations/1")
        print(f"   Compare Algorithms: {BASE_URL}/api/compare/1")
        
    else:
        print("\n❌ Some tests failed. Check the server logs for details.")

if __name__ == "__main__":
    main()
