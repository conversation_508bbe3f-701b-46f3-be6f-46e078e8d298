{% extends "base.html" %}

{% block title %}{{ movie.title }} - Movie Details{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item active">{{ movie.title }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Movie Details -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h1 class="mb-2">{{ movie.title }} <small class="text-muted">({{ movie.year }})</small></h1>
                        <p class="lead text-muted mb-3">Directed by {{ movie.director }}</p>
                        
                        <div class="mb-3">
                            {% for genre in movie.genres_list %}
                            <span class="badge bg-primary me-1">{{ genre }}</span>
                            {% endfor %}
                        </div>
                        
                        <p class="mb-3">{{ movie.description }}</p>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Duration:</strong> {{ movie.duration }} minutes
                            </div>
                            <div class="col-md-4">
                                <strong>IMDb Rating:</strong> 
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>{{ movie.imdb_rating }}
                                </span>
                            </div>
                            <div class="col-md-4">
                                <strong>User Rating:</strong> 
                                <span class="badge bg-info">
                                    <i class="fas fa-users me-1"></i>{{ avg_rating }} ({{ rating_count }} ratings)
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-center">
                        <div class="movie-poster bg-light d-flex align-items-center justify-content-center" 
                             style="height: 300px; border-radius: 10px;">
                            <div class="text-muted">
                                <i class="fas fa-film fa-4x mb-3"></i>
                                <p>Movie Poster</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Ratings -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>User Ratings ({{ rating_count }})
                </h5>
            </div>
            <div class="card-body">
                {% if movie_ratings %}
                <div class="row">
                    <div class="col-md-8">
                        <div style="max-height: 400px; overflow-y: auto;">
                            {% for rating, user in movie_ratings %}
                            <div class="card border-0 shadow-sm mb-2">
                                <div class="card-body py-2">
                                    <div class="row align-items-center">
                                        <div class="col-2">
                                            <div class="avatar-circle bg-primary text-white">
                                                {{ user.username[0].upper() }}
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-1">
                                                <a href="{{ url_for('user_profile', user_id=user.id) }}" 
                                                   class="text-decoration-none">
                                                    {{ user.username }}
                                                </a>
                                            </h6>
                                            <small class="text-muted">{{ user.occupation }}</small>
                                        </div>
                                        <div class="col-4 text-end">
                                            <span class="badge bg-warning text-dark">
                                                {% for i in range(rating.rating|int) %}
                                                <i class="fas fa-star"></i>
                                                {% endfor %}
                                                {% if rating.rating % 1 != 0 %}
                                                <i class="fas fa-star-half-alt"></i>
                                                {% endif %}
                                                {{ rating.rating }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5>Rating Distribution</h5>
                                <canvas id="ratingChart" width="200" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-star fa-3x mb-3"></i>
                    <p>No ratings yet. Be the first to rate this movie!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Similar Movies (if we had more data) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-film me-2"></i>Similar Movies
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center text-muted py-4">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>Similar movie recommendations would appear here with more data and advanced algorithms.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Create rating distribution chart
const ratings = [
    {% for rating, user in movie_ratings %}
    {{ rating.rating }},
    {% endfor %}
];

if (ratings.length > 0) {
    // Count ratings by value
    const ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    ratings.forEach(rating => {
        const rounded = Math.round(rating);
        if (ratingCounts.hasOwnProperty(rounded)) {
            ratingCounts[rounded]++;
        }
    });
    
    const ctx = document.getElementById('ratingChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['1 Star', '2 Stars', '3 Stars', '4 Stars', '5 Stars'],
            datasets: [{
                data: [ratingCounts[1], ratingCounts[2], ratingCounts[3], ratingCounts[4], ratingCounts[5]],
                backgroundColor: [
                    '#dc3545',
                    '#fd7e14',
                    '#ffc107',
                    '#28a745',
                    '#007bff'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>
{% endblock %}
