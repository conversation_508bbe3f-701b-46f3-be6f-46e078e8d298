import os
from pathlib import Path

class Config:
    """Configuration settings for the recommendation system."""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database settings
    BASE_DIR = Path(__file__).parent
    DATABASE_PATH = BASE_DIR / 'data' / 'recommendations.db'
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Recommendation settings
    MIN_RATINGS_FOR_RECOMMENDATION = 5
    DEFAULT_RECOMMENDATIONS_COUNT = 10
    SIMILARITY_THRESHOLD = 0.1
    
    # Data paths
    DATA_DIR = BASE_DIR / 'data'
    MOVIES_DATA_PATH = DATA_DIR / 'movies.csv'
    RATINGS_DATA_PATH = DATA_DIR / 'ratings.csv'
    USERS_DATA_PATH = DATA_DIR / 'users.csv'
    
    # Algorithm weights for hybrid recommendations
    COLLABORATIVE_WEIGHT = 0.6
    CONTENT_BASED_WEIGHT = 0.4
