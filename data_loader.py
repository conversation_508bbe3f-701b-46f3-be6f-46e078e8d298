import pandas as pd
import numpy as np
import json
import os
from pathlib import Path
from models import db, User, Movie, Rating
from config import Config

class DataLoader:
    """Class for loading and managing sample data."""

    def __init__(self):
        self.config = Config()
        self.data_dir = self.config.DATA_DIR
        self.ensure_data_directory()

    def ensure_data_directory(self):
        """Create data directory if it doesn't exist."""
        self.data_dir.mkdir(exist_ok=True)

    def create_sample_data(self):
        """Create sample movies, users, and ratings data."""
        self.create_sample_movies()
        self.create_sample_users()
        self.create_sample_ratings()

    def create_sample_movies(self):
        """Create sample movies dataset."""
        movies_data = [
            {
                'title': 'The Shawshank Redemption',
                'year': 1994,
                'genres': ['Drama'],
                'director': '<PERSON>',
                'description': 'Two imprisoned men bond over years, finding solace and redemption.',
                'imdb_rating': 9.3,
                'duration': 142
            },
            {
                'title': 'The Godfather',
                'year': 1972,
                'genres': ['Crime', 'Drama'],
                'director': '<PERSON>',
                'description': 'The aging patriarch of an organized crime dynasty transfers control.',
                'imdb_rating': 9.2,
                'duration': 175
            },
            {
                'title': 'The Dark Knight',
                'year': 2008,
                'genres': ['Action', 'Crime', 'Drama'],
                'director': 'Christopher Nolan',
                'description': 'Batman faces the Joker in this dark superhero tale.',
                'imdb_rating': 9.0,
                'duration': 152
            },
            {
                'title': 'Pulp Fiction',
                'year': 1994,
                'genres': ['Crime', 'Drama'],
                'director': 'Quentin Tarantino',
                'description': 'Interconnected stories of crime in Los Angeles.',
                'imdb_rating': 8.9,
                'duration': 154
            },
            {
                'title': 'Forrest Gump',
                'year': 1994,
                'genres': ['Drama', 'Romance'],
                'director': 'Robert Zemeckis',
                'description': 'Life story of a man with low IQ but good intentions.',
                'imdb_rating': 8.8,
                'duration': 142
            },
            {
                'title': 'Inception',
                'year': 2010,
                'genres': ['Action', 'Sci-Fi', 'Thriller'],
                'director': 'Christopher Nolan',
                'description': 'A thief enters dreams to plant ideas.',
                'imdb_rating': 8.8,
                'duration': 148
            },
            {
                'title': 'The Matrix',
                'year': 1999,
                'genres': ['Action', 'Sci-Fi'],
                'director': 'The Wachowskis',
                'description': 'A hacker discovers reality is a simulation.',
                'imdb_rating': 8.7,
                'duration': 136
            },
            {
                'title': 'Goodfellas',
                'year': 1990,
                'genres': ['Biography', 'Crime', 'Drama'],
                'director': 'Martin Scorsese',
                'description': 'The story of Henry Hill and his life in the mob.',
                'imdb_rating': 8.7,
                'duration': 146
            },
            {
                'title': 'Titanic',
                'year': 1997,
                'genres': ['Drama', 'Romance'],
                'director': 'James Cameron',
                'description': 'A love story aboard the ill-fated ship.',
                'imdb_rating': 7.8,
                'duration': 194
            },
            {
                'title': 'Avatar',
                'year': 2009,
                'genres': ['Action', 'Adventure', 'Fantasy'],
                'director': 'James Cameron',
                'description': 'A marine on an alien planet.',
                'imdb_rating': 7.8,
                'duration': 162
            },
            {
                'title': 'The Avengers',
                'year': 2012,
                'genres': ['Action', 'Adventure', 'Sci-Fi'],
                'director': 'Joss Whedon',
                'description': 'Superheroes team up to save Earth.',
                'imdb_rating': 8.0,
                'duration': 143
            },
            {
                'title': 'Interstellar',
                'year': 2014,
                'genres': ['Adventure', 'Drama', 'Sci-Fi'],
                'director': 'Christopher Nolan',
                'description': 'A team explores space to save humanity.',
                'imdb_rating': 8.6,
                'duration': 169
            }
        ]

        # Convert genres list to JSON string for CSV storage
        for movie in movies_data:
            movie['genres'] = json.dumps(movie['genres'])

        # Save to CSV
        df = pd.DataFrame(movies_data)
        df.to_csv(self.config.MOVIES_DATA_PATH, index=False)
        return movies_data

    def create_sample_users(self):
        """Create sample users dataset."""
        users_data = [
            {'username': 'alice_movie_lover', 'email': '<EMAIL>', 'age': 28, 'gender': 'F', 'occupation': 'Engineer'},
            {'username': 'bob_critic', 'email': '<EMAIL>', 'age': 35, 'gender': 'M', 'occupation': 'Teacher'},
            {'username': 'charlie_casual', 'email': '<EMAIL>', 'age': 22, 'gender': 'M', 'occupation': 'Student'},
            {'username': 'diana_cinephile', 'email': '<EMAIL>', 'age': 31, 'gender': 'F', 'occupation': 'Designer'},
            {'username': 'eve_action_fan', 'email': '<EMAIL>', 'age': 26, 'gender': 'F', 'occupation': 'Marketing'},
            {'username': 'frank_drama_lover', 'email': '<EMAIL>', 'age': 42, 'gender': 'M', 'occupation': 'Manager'},
            {'username': 'grace_sci_fi', 'email': '<EMAIL>', 'age': 29, 'gender': 'F', 'occupation': 'Scientist'},
            {'username': 'henry_classic', 'email': '<EMAIL>', 'age': 38, 'gender': 'M', 'occupation': 'Writer'}
        ]

        # Save to CSV
        df = pd.DataFrame(users_data)
        df.to_csv(self.config.USERS_DATA_PATH, index=False)
        return users_data

    def create_sample_ratings(self):
        """Create sample ratings dataset with realistic patterns."""
        np.random.seed(42)  # For reproducible results

        # Define user preferences (which genres they like)
        user_preferences = {
            1: ['Drama', 'Crime'],  # alice
            2: ['Drama', 'Biography'],  # bob
            3: ['Action', 'Adventure'],  # charlie
            4: ['Drama', 'Romance'],  # diana
            5: ['Action', 'Sci-Fi'],  # eve
            6: ['Drama', 'Crime'],  # frank
            7: ['Sci-Fi', 'Thriller'],  # grace
            8: ['Drama', 'Biography']  # henry
        }

        ratings_data = []

        # Load movies to get their genres
        movies_df = pd.read_csv(self.config.MOVIES_DATA_PATH)

        for user_id in range(1, 9):  # 8 users
            user_prefs = user_preferences[user_id]

            # Each user rates 6-10 movies
            num_ratings = np.random.randint(6, 11)
            movie_ids = np.random.choice(range(1, len(movies_df) + 1), num_ratings, replace=False)

            for movie_id in movie_ids:
                movie_row = movies_df.iloc[movie_id - 1]
                movie_genres = json.loads(movie_row['genres'])  # Convert JSON string to list

                # Base rating
                base_rating = np.random.normal(3.5, 1.0)

                # Adjust based on user preferences
                genre_match = any(genre in user_prefs for genre in movie_genres)
                if genre_match:
                    base_rating += np.random.normal(0.8, 0.3)
                else:
                    base_rating += np.random.normal(-0.3, 0.3)

                # Clamp to 1-5 range
                rating = max(1.0, min(5.0, base_rating))
                rating = round(rating * 2) / 2  # Round to nearest 0.5

                ratings_data.append({
                    'user_id': user_id,
                    'movie_id': movie_id,
                    'rating': rating
                })

        # Save to CSV
        df = pd.DataFrame(ratings_data)
        df.to_csv(self.config.RATINGS_DATA_PATH, index=False)
        return ratings_data

    def load_data_to_database(self):
        """Load all sample data into the database."""
        # Clear existing data
        db.drop_all()
        db.create_all()

        # Load movies
        movies_df = pd.read_csv(self.config.MOVIES_DATA_PATH)
        for _, row in movies_df.iterrows():
            movie = Movie(
                title=row['title'],
                year=row['year'],
                genres=row['genres'],
                director=row['director'],
                description=row['description'],
                imdb_rating=row['imdb_rating'],
                duration=row['duration']
            )
            db.session.add(movie)

        # Load users
        users_df = pd.read_csv(self.config.USERS_DATA_PATH)
        for _, row in users_df.iterrows():
            user = User(
                username=row['username'],
                email=row['email'],
                age=row['age'],
                gender=row['gender'],
                occupation=row['occupation']
            )
            db.session.add(user)

        # Commit movies and users first
        db.session.commit()

        # Load ratings
        ratings_df = pd.read_csv(self.config.RATINGS_DATA_PATH)
        for _, row in ratings_df.iterrows():
            rating = Rating(
                user_id=row['user_id'],
                movie_id=row['movie_id'],
                rating=row['rating']
            )
            db.session.add(rating)

        db.session.commit()
        print("Sample data loaded successfully!")
