{% extends "base.html" %}

{% block title %}{{ user.username }} - User Profile{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item active">{{ user.username }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- User Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="avatar-circle-large bg-primary text-white mx-auto">
                            {{ user.username[0].upper() }}
                        </div>
                    </div>
                    <div class="col-md-10">
                        <h2 class="mb-1">{{ user.username }}</h2>
                        <p class="text-muted mb-2">{{ user.email }}</p>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Age:</strong> {{ user.age }} years
                            </div>
                            <div class="col-md-3">
                                <strong>Gender:</strong> {{ user.gender }}
                            </div>
                            <div class="col-md-6">
                                <strong>Occupation:</strong> {{ user.occupation }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- User Ratings -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>Your Ratings ({{ user_ratings|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if user_ratings %}
                <div style="max-height: 400px; overflow-y: auto;">
                    {% for rating, movie in user_ratings %}
                    <div class="card border-0 shadow-sm mb-2">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-8">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('movie_details', movie_id=movie.id) }}" 
                                           class="text-decoration-none">
                                            {{ movie.title }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ movie.year }} • {{ movie.director }}</small>
                                </div>
                                <div class="col-4 text-end">
                                    <span class="badge bg-warning text-dark">
                                        {% for i in range(rating.rating|int) %}
                                        <i class="fas fa-star"></i>
                                        {% endfor %}
                                        {% if rating.rating % 1 != 0 %}
                                        <i class="fas fa-star-half-alt"></i>
                                        {% endif %}
                                        {{ rating.rating }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-star fa-3x mb-3"></i>
                    <p>No ratings yet. Start rating movies to get personalized recommendations!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-magic me-2"></i>Recommendations for You
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" 
                            id="algorithmDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>Algorithm
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="loadRecommendations('hybrid')">Hybrid</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadRecommendations('user_based')">User-Based</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadRecommendations('item_based')">Item-Based</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadRecommendations('content_based')">Content-Based</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadRecommendations('popular')">Popular</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div id="recommendations-container">
                    {% if recommendations %}
                    <div style="max-height: 400px; overflow-y: auto;">
                        {% for rec in recommendations %}
                        <div class="card border-0 shadow-sm mb-2">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-8">
                                        <h6 class="mb-1">
                                            <a href="{{ url_for('movie_details', movie_id=rec.movie.id) }}" 
                                               class="text-decoration-none">
                                                {{ rec.movie.title }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">{{ rec.movie.year }} • {{ rec.movie.director }}</small>
                                        <div class="mt-1">
                                            {% for genre in rec.movie.genres_list[:2] %}
                                            <span class="badge bg-light text-dark me-1">{{ genre }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="col-4 text-end">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-thumbs-up me-1"></i>{{ rec.predicted_rating }}
                                        </span>
                                        <div class="mt-1">
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="rateMovie({{ rec.movie.id }}, {{ user.id }})">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-magic fa-3x mb-3"></i>
                        <p>No recommendations available. Rate more movies to get better suggestions!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rate Movie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="movie-info" class="mb-3"></div>
                <div class="text-center">
                    <p>How would you rate this movie?</p>
                    <div class="rating-stars">
                        <i class="fas fa-star rating-star" data-rating="1"></i>
                        <i class="fas fa-star rating-star" data-rating="2"></i>
                        <i class="fas fa-star rating-star" data-rating="3"></i>
                        <i class="fas fa-star rating-star" data-rating="4"></i>
                        <i class="fas fa-star rating-star" data-rating="5"></i>
                    </div>
                    <p class="mt-2">Selected Rating: <span id="selected-rating">0</span>/5</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitRating()">Submit Rating</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentMovieId = null;
let currentUserId = {{ user.id }};
let selectedRating = 0;

function loadRecommendations(algorithm) {
    const container = document.getElementById('recommendations-container');
    container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
    
    fetch(`/api/recommendations/{{ user.id }}?algorithm=${algorithm}&count=8`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecommendations(data.recommendations);
            } else {
                container.innerHTML = '<div class="alert alert-danger">Error loading recommendations</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="alert alert-danger">Error loading recommendations</div>';
        });
}

function displayRecommendations(recommendations) {
    const container = document.getElementById('recommendations-container');
    
    if (recommendations.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-magic fa-3x mb-3"></i><p>No recommendations available.</p></div>';
        return;
    }
    
    let html = '<div style="max-height: 400px; overflow-y: auto;">';
    recommendations.forEach(rec => {
        html += `
            <div class="card border-0 shadow-sm mb-2">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h6 class="mb-1">${rec.title}</h6>
                            <small class="text-muted">${rec.year} • ${rec.director}</small>
                            <div class="mt-1">
                                ${rec.genres.slice(0, 2).map(genre => `<span class="badge bg-light text-dark me-1">${genre}</span>`).join('')}
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <span class="badge bg-primary">
                                <i class="fas fa-thumbs-up me-1"></i>${rec.predicted_rating}
                            </span>
                            <div class="mt-1">
                                <button class="btn btn-sm btn-outline-success" onclick="rateMovie(${rec.movie_id}, ${currentUserId})">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    container.innerHTML = html;
}

function rateMovie(movieId, userId) {
    currentMovieId = movieId;
    
    // Get movie info
    fetch('/api/movies')
        .then(response => response.json())
        .then(data => {
            const movie = data.movies.find(m => m.id === movieId);
            if (movie) {
                document.getElementById('movie-info').innerHTML = `
                    <h6>${movie.title} (${movie.year})</h6>
                    <p class="text-muted">${movie.director}</p>
                `;
            }
        });
    
    // Reset rating
    selectedRating = 0;
    updateStars();
    document.getElementById('selected-rating').textContent = '0';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('ratingModal')).show();
}

// Rating stars functionality
document.querySelectorAll('.rating-star').forEach(star => {
    star.addEventListener('click', function() {
        selectedRating = parseInt(this.dataset.rating);
        document.getElementById('selected-rating').textContent = selectedRating;
        updateStars();
    });
    
    star.addEventListener('mouseover', function() {
        const rating = parseInt(this.dataset.rating);
        highlightStars(rating);
    });
});

document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
    updateStars();
});

function highlightStars(rating) {
    document.querySelectorAll('.rating-star').forEach((star, index) => {
        if (index < rating) {
            star.classList.add('text-warning');
            star.classList.remove('text-muted');
        } else {
            star.classList.add('text-muted');
            star.classList.remove('text-warning');
        }
    });
}

function updateStars() {
    highlightStars(selectedRating);
}

function submitRating() {
    if (selectedRating === 0) {
        alert('Please select a rating');
        return;
    }
    
    fetch('/api/rate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user_id: currentUserId,
            movie_id: currentMovieId,
            rating: selectedRating
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('ratingModal')).hide();
            location.reload(); // Refresh to show updated ratings
        } else {
            alert('Error submitting rating: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error submitting rating');
    });
}

// Initialize stars
updateStars();
</script>
{% endblock %}
