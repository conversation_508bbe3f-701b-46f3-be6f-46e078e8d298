from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
import os
from config import Config
from models import db, User, Movie, Rating
from data_loader import DataLoader
from recommendation_engine import RecommendationEngine

app = Flask(__name__)
app.config.from_object(Config)

# Initialize database
db.init_app(app)

# Global recommendation engine instance
recommendation_engine = None

def initialize_data():
    """Initialize sample data and recommendation engine."""
    global recommendation_engine
    
    with app.app_context():
        # Create data directory if it doesn't exist
        os.makedirs(app.config['DATA_DIR'], exist_ok=True)
        
        # Create database tables
        db.create_all()
        
        # Check if we need to load sample data
        if Movie.query.count() == 0:
            print("Loading sample data...")
            data_loader = DataLoader()
            data_loader.create_sample_data()
            data_loader.load_data_to_database()
        
        # Initialize recommendation engine
        recommendation_engine = RecommendationEngine()
        print("Recommendation system initialized!")

@app.route('/')
def index():
    """Home page showing all movies and users."""
    movies = Movie.query.all()
    users = User.query.all()
    return render_template('index.html', movies=movies, users=users)

@app.route('/user/<int:user_id>')
def user_profile(user_id):
    """User profile page showing ratings and recommendations."""
    user = User.query.get_or_404(user_id)
    
    # Get user's ratings
    user_ratings = db.session.query(Rating, Movie).join(Movie).filter(
        Rating.user_id == user_id
    ).all()
    
    # Get recommendations
    try:
        recommendations = recommendation_engine.get_recommendations(
            user_id, algorithm='hybrid', n_recommendations=8
        )
        
        # Get movie details for recommendations
        rec_movies = []
        for rec in recommendations:
            movie = Movie.query.get(rec['movie_id'])
            if movie:
                rec_movies.append({
                    'movie': movie,
                    'predicted_rating': round(rec['predicted_rating'], 2)
                })
    except Exception as e:
        print(f"Error getting recommendations: {e}")
        rec_movies = []
    
    return render_template('user_profile.html', 
                         user=user, 
                         user_ratings=user_ratings,
                         recommendations=rec_movies)

@app.route('/movie/<int:movie_id>')
def movie_details(movie_id):
    """Movie details page."""
    movie = Movie.query.get_or_404(movie_id)
    
    # Get movie ratings
    movie_ratings = db.session.query(Rating, User).join(User).filter(
        Rating.movie_id == movie_id
    ).all()
    
    # Calculate average rating
    ratings = [r.Rating.rating for r in movie_ratings]
    avg_rating = sum(ratings) / len(ratings) if ratings else 0
    
    return render_template('movie_details.html', 
                         movie=movie, 
                         movie_ratings=movie_ratings,
                         avg_rating=round(avg_rating, 2),
                         rating_count=len(ratings))

@app.route('/api/recommendations/<int:user_id>')
def api_recommendations(user_id):
    """API endpoint for getting recommendations."""
    algorithm = request.args.get('algorithm', 'hybrid')
    n_recommendations = int(request.args.get('count', 10))
    
    try:
        recommendations = recommendation_engine.get_recommendations(
            user_id, algorithm=algorithm, n_recommendations=n_recommendations
        )
        
        # Get movie details
        result = []
        for rec in recommendations:
            movie = Movie.query.get(rec['movie_id'])
            if movie:
                result.append({
                    'movie_id': movie.id,
                    'title': movie.title,
                    'year': movie.year,
                    'genres': movie.genres_list,
                    'director': movie.director,
                    'predicted_rating': round(rec['predicted_rating'], 2)
                })
        
        return jsonify({
            'success': True,
            'user_id': user_id,
            'algorithm': algorithm,
            'recommendations': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/rate', methods=['POST'])
def api_rate_movie():
    """API endpoint for rating a movie."""
    data = request.get_json()
    
    try:
        user_id = int(data['user_id'])
        movie_id = int(data['movie_id'])
        rating = float(data['rating'])
        
        # Validate rating
        if not (1 <= rating <= 5):
            return jsonify({
                'success': False,
                'error': 'Rating must be between 1 and 5'
            }), 400
        
        # Add rating
        success = recommendation_engine.add_rating(user_id, movie_id, rating)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Rating added successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to add rating'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/movies')
def api_movies():
    """API endpoint for getting all movies."""
    movies = Movie.query.all()
    return jsonify({
        'success': True,
        'movies': [movie.to_dict() for movie in movies]
    })

@app.route('/api/users')
def api_users():
    """API endpoint for getting all users."""
    users = User.query.all()
    return jsonify({
        'success': True,
        'users': [user.to_dict() for user in users]
    })

@app.route('/compare')
def compare_algorithms():
    """Page for comparing different recommendation algorithms."""
    users = User.query.all()
    return render_template('compare.html', users=users)

@app.route('/api/compare/<int:user_id>')
def api_compare_algorithms(user_id):
    """API endpoint for comparing algorithms for a specific user."""
    algorithms = ['user_based', 'item_based', 'content_based', 'hybrid', 'popular']
    results = {}
    
    for algorithm in algorithms:
        try:
            recommendations = recommendation_engine.get_recommendations(
                user_id, algorithm=algorithm, n_recommendations=5
            )
            
            # Get movie details
            movie_recs = []
            for rec in recommendations:
                movie = Movie.query.get(rec['movie_id'])
                if movie:
                    movie_recs.append({
                        'title': movie.title,
                        'year': movie.year,
                        'predicted_rating': round(rec['predicted_rating'], 2)
                    })
            
            results[algorithm] = movie_recs
        
        except Exception as e:
            results[algorithm] = {'error': str(e)}
    
    return jsonify({
        'success': True,
        'user_id': user_id,
        'results': results
    })

if __name__ == '__main__':
    initialize_data()
    app.run(debug=True, host='0.0.0.0', port=5000)
