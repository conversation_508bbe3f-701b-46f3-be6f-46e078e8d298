import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from scipy.sparse import csr_matrix
from models import db, User, Movie, Rating
from config import Config
import json

class RecommendationEngine:
    """Main recommendation engine with multiple algorithms."""

    def __init__(self):
        self.config = Config()
        self.user_item_matrix = None
        self.item_features_matrix = None
        self.user_similarity_matrix = None
        self.item_similarity_matrix = None
        self.content_similarity_matrix = None
        self._load_data()

    def _load_data(self):
        """Load data from database and prepare matrices."""
        # Load ratings data
        ratings_query = db.session.query(Rating).all()
        self.ratings_df = pd.DataFrame([{
            'user_id': r.user_id,
            'movie_id': r.movie_id,
            'rating': r.rating
        } for r in ratings_query])

        # Load movies data
        movies_query = db.session.query(Movie).all()
        self.movies_df = pd.DataFrame([{
            'movie_id': m.id,
            'title': m.title,
            'genres': m.genres_list,
            'director': m.director,
            'description': m.description,
            'year': m.year,
            'imdb_rating': m.imdb_rating
        } for m in movies_query])

        # Load users data
        users_query = db.session.query(User).all()
        self.users_df = pd.DataFrame([{
            'user_id': u.id,
            'username': u.username,
            'age': u.age,
            'gender': u.gender,
            'occupation': u.occupation
        } for u in users_query])

        self._prepare_matrices()

    def _prepare_matrices(self):
        """Prepare user-item matrix and content features."""
        if self.ratings_df.empty:
            return

        # Create user-item matrix
        self.user_item_matrix = self.ratings_df.pivot(
            index='user_id',
            columns='movie_id',
            values='rating'
        ).fillna(0)

        # Prepare content features for movies
        self._prepare_content_features()

        # Calculate similarity matrices
        self._calculate_similarities()

    def _prepare_content_features(self):
        """Prepare content-based features for movies."""
        if self.movies_df.empty:
            return

        # Combine text features
        content_features = []
        for _, movie in self.movies_df.iterrows():
            # Combine genres, director, and description
            genres_list = movie['genres'] if isinstance(movie['genres'], list) else []
            genres_str = ' '.join(genres_list) if genres_list else ''
            director_str = movie['director'] if movie['director'] else ''
            description_str = movie['description'] if movie['description'] else ''

            combined_text = f"{genres_str} {director_str} {description_str}"
            content_features.append(combined_text)

        # Create TF-IDF matrix
        tfidf = TfidfVectorizer(stop_words='english', max_features=1000)
        self.item_features_matrix = tfidf.fit_transform(content_features)

    def _calculate_similarities(self):
        """Calculate user-user and item-item similarity matrices."""
        if self.user_item_matrix is None or self.user_item_matrix.empty:
            return

        # User-based collaborative filtering similarity
        user_matrix = self.user_item_matrix.values
        self.user_similarity_matrix = cosine_similarity(user_matrix)

        # Item-based collaborative filtering similarity
        item_matrix = self.user_item_matrix.T.values
        self.item_similarity_matrix = cosine_similarity(item_matrix)

        # Content-based similarity
        if self.item_features_matrix is not None:
            self.content_similarity_matrix = cosine_similarity(self.item_features_matrix)

    def get_user_based_recommendations(self, user_id, n_recommendations=10):
        """Get recommendations using user-based collaborative filtering."""
        if (self.user_similarity_matrix is None or
            user_id not in self.user_item_matrix.index):
            return []

        user_idx = list(self.user_item_matrix.index).index(user_id)
        user_similarities = self.user_similarity_matrix[user_idx]

        # Get similar users (excluding the user themselves)
        similar_users_idx = np.argsort(user_similarities)[::-1][1:]

        # Get movies rated by similar users but not by target user
        user_ratings = self.user_item_matrix.loc[user_id]
        unrated_movies = user_ratings[user_ratings == 0].index

        recommendations = {}

        for movie_id in unrated_movies:
            if movie_id not in self.user_item_matrix.columns:
                continue

            weighted_sum = 0
            similarity_sum = 0

            for similar_user_idx in similar_users_idx[:10]:  # Top 10 similar users
                similar_user_id = self.user_item_matrix.index[similar_user_idx]
                similarity = user_similarities[similar_user_idx]

                if similarity > self.config.SIMILARITY_THRESHOLD:
                    rating = self.user_item_matrix.loc[similar_user_id, movie_id]
                    if rating > 0:
                        weighted_sum += similarity * rating
                        similarity_sum += similarity

            if similarity_sum > 0:
                predicted_rating = weighted_sum / similarity_sum
                recommendations[movie_id] = predicted_rating

        # Sort by predicted rating and return top N
        sorted_recommendations = sorted(
            recommendations.items(),
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

        return [{'movie_id': movie_id, 'predicted_rating': rating}
                for movie_id, rating in sorted_recommendations]

    def get_item_based_recommendations(self, user_id, n_recommendations=10):
        """Get recommendations using item-based collaborative filtering."""
        if (self.item_similarity_matrix is None or
            user_id not in self.user_item_matrix.index):
            return []

        user_ratings = self.user_item_matrix.loc[user_id]
        rated_movies = user_ratings[user_ratings > 0]
        unrated_movies = user_ratings[user_ratings == 0].index

        recommendations = {}

        for movie_id in unrated_movies:
            if movie_id not in self.user_item_matrix.columns:
                continue

            movie_idx = list(self.user_item_matrix.columns).index(movie_id)
            movie_similarities = self.item_similarity_matrix[movie_idx]

            weighted_sum = 0
            similarity_sum = 0

            for rated_movie_id, rating in rated_movies.items():
                if rated_movie_id in self.user_item_matrix.columns:
                    rated_movie_idx = list(self.user_item_matrix.columns).index(rated_movie_id)
                    similarity = movie_similarities[rated_movie_idx]

                    if similarity > self.config.SIMILARITY_THRESHOLD:
                        weighted_sum += similarity * rating
                        similarity_sum += similarity

            if similarity_sum > 0:
                predicted_rating = weighted_sum / similarity_sum
                recommendations[movie_id] = predicted_rating

        # Sort by predicted rating and return top N
        sorted_recommendations = sorted(
            recommendations.items(),
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

        return [{'movie_id': movie_id, 'predicted_rating': rating}
                for movie_id, rating in sorted_recommendations]

    def get_content_based_recommendations(self, user_id, n_recommendations=10):
        """Get recommendations using content-based filtering."""
        if (self.content_similarity_matrix is None or
            user_id not in self.user_item_matrix.index):
            return []

        user_ratings = self.user_item_matrix.loc[user_id]
        rated_movies = user_ratings[user_ratings > 0]
        unrated_movies = user_ratings[user_ratings == 0].index

        # Create user profile based on rated movies
        user_profile = np.zeros(self.content_similarity_matrix.shape[0])

        for movie_id, rating in rated_movies.items():
            if movie_id in self.movies_df['movie_id'].values:
                movie_idx = self.movies_df[self.movies_df['movie_id'] == movie_id].index[0]
                user_profile += rating * self.content_similarity_matrix[movie_idx]

        # Normalize user profile
        if np.sum(user_profile) > 0:
            user_profile = user_profile / len(rated_movies)

        recommendations = {}

        for movie_id in unrated_movies:
            if movie_id in self.movies_df['movie_id'].values:
                movie_idx = self.movies_df[self.movies_df['movie_id'] == movie_id].index[0]
                similarity_score = np.dot(user_profile, self.content_similarity_matrix[movie_idx])
                recommendations[movie_id] = similarity_score

        # Sort by similarity score and return top N
        sorted_recommendations = sorted(
            recommendations.items(),
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

        return [{'movie_id': movie_id, 'predicted_rating': rating}
                for movie_id, rating in sorted_recommendations]

    def get_hybrid_recommendations(self, user_id, n_recommendations=10):
        """Get recommendations using hybrid approach (collaborative + content-based)."""
        # Get recommendations from both approaches
        collaborative_recs = self.get_user_based_recommendations(user_id, n_recommendations * 2)
        content_recs = self.get_content_based_recommendations(user_id, n_recommendations * 2)

        # Combine recommendations with weights
        combined_scores = {}

        # Add collaborative filtering scores
        for rec in collaborative_recs:
            movie_id = rec['movie_id']
            score = rec['predicted_rating'] * self.config.COLLABORATIVE_WEIGHT
            combined_scores[movie_id] = combined_scores.get(movie_id, 0) + score

        # Add content-based scores
        for rec in content_recs:
            movie_id = rec['movie_id']
            score = rec['predicted_rating'] * self.config.CONTENT_BASED_WEIGHT
            combined_scores[movie_id] = combined_scores.get(movie_id, 0) + score

        # Sort by combined score and return top N
        sorted_recommendations = sorted(
            combined_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

        return [{'movie_id': movie_id, 'predicted_rating': rating}
                for movie_id, rating in sorted_recommendations]

    def get_popular_recommendations(self, n_recommendations=10):
        """Get popular movies as fallback recommendations."""
        if self.ratings_df.empty:
            return []

        # Calculate average rating and rating count for each movie
        movie_stats = self.ratings_df.groupby('movie_id').agg({
            'rating': ['mean', 'count']
        }).round(2)

        movie_stats.columns = ['avg_rating', 'rating_count']
        movie_stats = movie_stats.reset_index()

        # Filter movies with minimum number of ratings
        min_ratings = max(1, self.config.MIN_RATINGS_FOR_RECOMMENDATION // 2)
        popular_movies = movie_stats[movie_stats['rating_count'] >= min_ratings]

        # Sort by average rating
        popular_movies = popular_movies.sort_values('avg_rating', ascending=False)

        return [{'movie_id': row['movie_id'], 'predicted_rating': row['avg_rating']}
                for _, row in popular_movies.head(n_recommendations).iterrows()]

    def get_recommendations(self, user_id, algorithm='hybrid', n_recommendations=10):
        """Get recommendations using specified algorithm."""
        if algorithm == 'user_based':
            return self.get_user_based_recommendations(user_id, n_recommendations)
        elif algorithm == 'item_based':
            return self.get_item_based_recommendations(user_id, n_recommendations)
        elif algorithm == 'content_based':
            return self.get_content_based_recommendations(user_id, n_recommendations)
        elif algorithm == 'hybrid':
            return self.get_hybrid_recommendations(user_id, n_recommendations)
        elif algorithm == 'popular':
            return self.get_popular_recommendations(n_recommendations)
        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")

    def add_rating(self, user_id, movie_id, rating):
        """Add a new rating and update matrices."""
        # Add to database
        existing_rating = Rating.query.filter_by(user_id=user_id, movie_id=movie_id).first()
        if existing_rating:
            existing_rating.rating = rating
        else:
            new_rating = Rating(user_id=user_id, movie_id=movie_id, rating=rating)
            db.session.add(new_rating)

        db.session.commit()

        # Reload data and recalculate matrices
        self._load_data()

        return True